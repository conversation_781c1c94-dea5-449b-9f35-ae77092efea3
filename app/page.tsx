"use client"

import { MainLayout } from "@/components/layout/MainLayout"
import { ThemeDemo } from "@/components/theme/ThemeDemo"
import { LanguageDemo } from "@/components/demo/LanguageDemo"
import { useTranslation } from "react-i18next"

export default function Home() {
  const { t } = useTranslation()
  return (
    <MainLayout>
      <div className="space-y-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-slate-100">
          {t("demo.title")}
        </h1>
        <p className="text-lg text-gray-700 dark:text-slate-300">
          {t("demo.description")}
        </p>
        <LanguageDemo />
        <ThemeDemo />
      </div>
    </MainLayout>
  )
}
