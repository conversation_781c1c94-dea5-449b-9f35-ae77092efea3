'use client';

import { useLanguageStore } from '@/stores/languageStore';
import { useTranslation } from 'react-i18next';

export function LanguageToggle() {
  const { language, toggleLanguage } = useLanguageStore();
  const { t } = useTranslation();

  return (
    <button
      onClick={toggleLanguage}
      className="
        flex items-center justify-center
        w-10 h-10 rounded-lg
        bg-gray-100 hover:bg-gray-200
        dark:bg-gray-800 dark:hover:bg-gray-700
        transition-colors duration-200
        border border-gray-200 dark:border-gray-700
        text-sm font-medium
        text-gray-700 dark:text-gray-300
      "
      title={t('language.toggle_language')}
      aria-label={t('language.toggle_language')}
    >
      {language === 'en' ? 'ع' : 'EN'}
    </button>
  );
}
